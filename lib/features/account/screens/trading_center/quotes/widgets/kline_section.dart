import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/time_range.dart';
import 'package:gp_stock_app/features/account/domain/constants/kline_constants.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/kline_selector.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/tick_list_section.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_kline_data.dart';
import 'package:gp_stock_app/features/market/logic/market_status/market_status_cubit.dart';

import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/constants/web_socket_actions.dart';
import 'package:gp_stock_app/shared/constants/web_socket_types.dart';
import 'package:gp_stock_app/shared/mixin/web_socket_mixin.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';
import 'package:gp_stock_app/shared/models/web_scoket_message.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:flutter_chen_kchart/k_chart.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/chen_kchart_data_adapter.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/chen_kchart_theme_adapter.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/chart_period_type.dart';

class KlineSection extends StatefulWidget {
  const KlineSection({super.key, required this.instrument});

  final Instrument instrument;

  @override
  State<KlineSection> createState() => _KlineSectionState();
}

class _KlineSectionState extends State<KlineSection> with WebSocketMixin {
  StreamSubscription<WebSocketMessage>? _marketSubscription;

  // Chart controller for programmatic control
  final KChartController _chartController = KChartController();

  int? _current5MinBucket;

  /// Calculate expected data points for intraday charts based on market type
  int _getExpectedDataPoints(ChartPeriodType periodType, TodaysTab marketTab) {
    if (!periodType.isIntradayType) {
      return 0;
    }

    final mainMarketType = switch (marketTab) {
      TodaysTab.aShares => MainMarketType.cnShares,
      TodaysTab.hkShares => MainMarketType.hkShares,
      TodaysTab.usShares => MainMarketType.usShares,
    };

    return TimeRange.shareMinutesPerDay(mainMarketType);
  }

  /// Round timestamp to nearest 5-minute boundary for 5-day charts
  int _roundTo5MinuteBoundary(int timestamp) {
    final seconds = timestamp;
    final remainder = seconds % 300;
    // Round to nearest 5-minute boundary
    return remainder < 150
        ? seconds - remainder // Round down
        : seconds + (300 - remainder); // Round up
  }

  /// Only updates when moving to a new 5-minute interval to prevent excessive re-renders
  bool _shouldUpdateChart5Day(int timestamp) {
    final newBucket = (timestamp ~/ 300) * 300;
    if (_current5MinBucket != newBucket) {
      _current5MinBucket = newBucket;
      return true; // New bucket - update chart
    }
    return false; // Same bucket - skip update to maintain stable positioning
  }

  void _listenToSocketUpdates() {
    // Listen for market data updates
    _marketSubscription = onMessage(SocketEvents.market).listen(_handleMarketUpdate);

    // Subscribe to timeline data for this instrument
    webSocketService.send({
      'type': SocketEvents.market,
      'action': SocketActions.timeLine,
      'params': {
        'instrument': widget.instrument.instrument,
        'period': 'day',
        'operate': 'subscribe',
      }
    });
  }

  void _handleMarketUpdate(WebSocketMessage message) {
    // Validate response code
    if (message.data['code'] != 200) return;

    // Parse and validate message data
    final stockMarketUpdate = StockKlineResponse.fromJson(message.data);
    if (stockMarketUpdate.data == null) return;

    // Check if this update is for our instrument
    final isMatchingInstrument = stockMarketUpdate.data?.detail?.instrument == widget.instrument.instrument;
    if (!isMatchingInstrument) return;

    // Check if market is open
    final isMarketOpen = getIt<MarketStatusCubit>().isMarketOpen(stockMarketUpdate.data?.detail?.market);
    if (!isMarketOpen) return;

    final tradingCubit = context.read<TradingCubit>();
    final currentKlineData = tradingCubit.state.klineDetailList;

    if (currentKlineData != null && currentKlineData.data != null && stockMarketUpdate.data?.list != null) {
      final newKlineItems = stockMarketUpdate.data!.list!;
      final existingList = currentKlineData.data!.list ?? [];
      List<KlineItem> updatedList = [];

      final currentState = tradingCubit.state;
      final isLine = currentState.klineOption?.type == "timeLine";
      final periodType = ChartPeriodType.fromId(currentState.klineOption?.id ?? "intraday");
      final is5DayLineChart = periodType.isFiveDay && isLine;

      // Handle real-time price updates (single item with price)
      if (newKlineItems.length == 1 && newKlineItems.first.price != null) {
        // For 5-day line charts, round timestamp to 5-minute boundary
        final processedItems = is5DayLineChart
            ? newKlineItems.map((item) {
                final originalTime = item.time ?? 0;
                final roundedTime = _roundTo5MinuteBoundary(originalTime);

                return item.copyWith(time: roundedTime);
              }).toList()
            : newKlineItems;
        // Add existing items, replacing the last one if it has the same timestamp
        for (int i = 0; i < existingList.length; i++) {
          final existingItem = existingList[i];
          final isLastItem = i == existingList.length - 1;
          final hasSameTimeAsNew = processedItems.any((newItem) => newItem.time == existingItem.time);

          if (isLastItem && hasSameTimeAsNew) {
            continue; // Skip last item to replace with new data
          }
          updatedList.add(existingItem);
        }
        updatedList.addAll(processedItems);
      } else {
        // Handle regular historical data updates
        final processedItems = is5DayLineChart
            ? newKlineItems.map((item) {
                final roundedTime = _roundTo5MinuteBoundary(item.time ?? 0);
                return item.copyWith(time: roundedTime);
              }).toList()
            : newKlineItems;

        for (final existingItem in existingList) {
          final hasConflict = processedItems.any((newItem) => newItem.time == existingItem.time);
          if (!hasConflict) {
            updatedList.add(existingItem);
          }
        }
        updatedList.addAll(processedItems);
      }

      // Sort by time to maintain chronological order
      updatedList.sort((a, b) => (a.time ?? 0).compareTo(b.time ?? 0));

      // Update the kline data
      final updatedKlineData = currentKlineData.copyWith(
        data: currentKlineData.data!.copyWith(
          list: updatedList,
          detail: stockMarketUpdate.data?.detail,
        ),
      );

      // For 5-day line charts, only update when bucket changes to prevent re-renders
      if (is5DayLineChart && mounted) {
        final shouldUpdate = updatedList.isNotEmpty ? _shouldUpdateChart5Day(updatedList.last.time ?? 0) : false;
        if (shouldUpdate) {
          tradingCubit.updateKlineDetailList(updatedKlineData);
        }
      } else if (mounted) {
        tradingCubit.updateKlineDetailList(updatedKlineData);
      }
    }
  }

  @override
  void initState() {
    super.initState();
    context.read<TradingCubit>().getKlineDetailList(widget.instrument.instrument, KlineConstants.options[0]);
    _listenToSocketUpdates();
  }

  void _resetBucketTracking() {
    _current5MinBucket = null;
  }

  @override
  void dispose() {
    _marketSubscription?.cancel();
    // Note: KChartController doesn't need explicit disposal in this version
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: context.theme.cardColor,
      child: Column(
        children: [
          KlineSelector(instrument: widget.instrument.instrument),
          Divider(
            color: context.theme.dividerColor,
            height: 1.gw,
          ),
          BlocBuilder<TradingCubit, TradingState>(
            builder: (context, state) {
              if (state.klineDetailListStatus == DataStatus.loading) {
                return ShimmerWidget(height: 180.gw);
              }

              if (state.klineDetailListStatus == DataStatus.failed) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error, color: Colors.red),
                      SizedBox(height: 8.gw),
                      Text("chart_failed_to_load".tr(), style: TextStyle(fontSize: 12, color: Colors.grey)),
                    ],
                  ),
                );
              }

              final isLine = state.klineOption?.type == "timeLine";
              final periodType = ChartPeriodType.fromId(state.klineOption?.id ?? "intraday");
              final showTicks = periodType.isIntraday && !state.isIndexTrading;

              // Reset bucket tracking when chart type changes
              final is5DayLineChart = periodType.isFiveDay && isLine;
              if (!is5DayLineChart) {
                _resetBucketTracking();
              }
              // Process data and render chart
              final originalClosePrice = state.stockInfo?.close;
              var chartData = ChenKChartDataAdapter.convertKlineData(
                state.klineDetailList?.data?.list,
                isLine: isLine,
                originalClosePrice: originalClosePrice,
              );

              // For 5-day line charts, filter to 5-minute intervals to reduce data points
              if (periodType.isFiveDay && isLine && chartData.length > 5) {
                chartData = chartData.where((entry) => (entry.time ?? -1) / 1000 % 300 == 0).toList();
              }

              return Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(8, 0, 8, 4),
                      child: chartData.isEmpty
                          ? SizedBox(
                              height: 180.gw,
                              child: Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.timeline, color: Colors.grey.withValues(alpha: 0.5), size: 32.gw),
                                    SizedBox(height: 8.gw),
                                    Text(
                                      "chart_waiting_for_data".tr(),
                                      style: TextStyle(fontSize: 12.gsp, color: Colors.grey),
                                    ),
                                  ],
                                ),
                              ),
                            )
                          : LayoutBuilder(
                              builder: (context, constraints) {
                                // Configure global theme
                                ChenKChartThemeAdapter.configureGlobalTheme(context);

                                // Get market type from instrument
                                final marketTab =
                                    ChenKChartDataAdapter.getMarketFromInstrument(widget.instrument.instrument);

                                // Calculate expected data points for consistent chart width
                                final expectedDataPoints = periodType.isIntradayType
                                    ? _getExpectedDataPoints(periodType, marketTab)
                                    : chartData.length;

                                // Create responsive chart style and colors
                                var chartStyle = ChenKChartThemeAdapter.createChartStyle(
                                  context: context,
                                  periodType: periodType,
                                  screenWidth: constraints.maxWidth,
                                  screenHeight: constraints.maxHeight,
                                  expectedDataPoints: expectedDataPoints,
                                  marketTab: marketTab,
                                );

                                // Update with 5-day date labels if needed
                                chartStyle = ChenKChartThemeAdapter.update5DayLabels(
                                    chartStyle, periodType, chartData, marketTab, constraints.maxWidth);

                                final chartColors = ChenKChartThemeAdapter.createChartColors(context);

                                // Get responsive configuration
                                final maDayList = ChenKChartThemeAdapter.getMADayList(
                                  periodType: periodType,
                                  screenWidth: constraints.maxWidth,
                                );
                                final timeFormat = ChenKChartThemeAdapter.getTimeFormat(
                                  periodType: periodType,
                                  isLine: isLine,
                                );
                                final mainState = ChenKChartThemeAdapter.getMainState();

                                return Padding(
                                  padding: const EdgeInsets.fromLTRB(0, 10, 0, 0),
                                  child: SizedBox(
                                    height: 0.30.gsh,
                                    child: KChartWidget(
                                      chartData,
                                      chartStyle: chartStyle,
                                      chartColors: chartColors,
                                      controller: _chartController,
                                      isTrendLine: false,
                                      mainState: mainState,
                                      secondaryState: SecondaryState.NONE,
                                      volHidden: false,
                                      isTapShowInfoDialog: true,
                                      timeFormat: timeFormat,
                                      verticalTextAlignment: VerticalTextAlignment.right,
                                      isLine: isLine,
                                      xFrontPadding: 0,
                                      maDayList: maDayList,
                                      enableTheme: true,
                                      showNowPrice: true,
                                      showInfoDialog: true,
                                      materialInfoDialog: true,
                                      fixedLength: 4,
                                      // Enhanced features
                                      enablePinchZoom: true,
                                      enableScrollZoom: true,
                                      scaleSensitivity: 2.5,
                                      minScale: 0.1,
                                      maxScale: 5.0,
                                      enableScaleAnimation: true,
                                      enablePerformanceMode: false,
                                      onLoadMore: (bool isLoadingMore) {
                                        // Handle load more functionality
                                      },
                                      onScaleChanged: (double scale) {
                                        // Handle scale changes
                                      },
                                    ),
                                  ),
                                );
                              },
                            ),
                    ),
                  ),
                  if (showTicks)
                    TickListSection(
                        instrument: widget.instrument, getColorCallback: (value) => value.getValueColor(context)),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}

class EmptyChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.1)
      ..strokeWidth = 1;

    // Draw grid lines
    final gridSpacing = size.height / 5;
    for (int i = 1; i < 5; i++) {
      final y = gridSpacing * i;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // Draw vertical lines
    final verticalSpacing = size.width / 6;
    for (int i = 1; i < 6; i++) {
      final x = verticalSpacing * i;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
